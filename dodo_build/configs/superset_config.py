# Superset Configuration file
# add file superset_config.py to PYTHON<PERSON>TH for usage
import importlib
import logging
import os
import sys
from datetime import timedelta

from sqlalchemy.pool import QueuePool
from utils import get_env_variable, is_development
from version import VERSION

logger = logging.getLogger(__name__)
logger.addHandler(logging.StreamHandler())

DODO_VERSION = VERSION

REDIS_HOST = get_env_variable("REDIS_HOST")
REDIS_PORT = get_env_variable("REDIS_PORT")
REDIS_PASSWORD = get_env_variable("REDIS_PASSWORD")
REDIS_RATELIMIT_DB = get_env_variable("REDIS_RATELIMIT_DB", 0)

# Metadata database
SQLALCHEMY_DATABASE_URI = get_env_variable("SQLALCHEMY_DATABASE_URI")

SQLALCHEMY_ENGINE_OPTIONS = {
    "poolclass": QueuePool,
    "pool_size": 30,
    "max_overflow": 20,
    "pool_recycle": 120,
    "connect_args": {"connect_timeout": 5},
    "hide_parameters": True,
}

SUPERSET_DASHBOARD_POSITION_DATA_LIMIT = 65535 * 4

# Set this API key to enable Mapbox visualizations
# MAPBOX_API_KEY = get_env_variable("MAPBOX_API_KEY", "mapbox-api-key")
MAPBOX_API_KEY = get_env_variable(
    "MAPBOX_API_KEY",
    "pk.eyJ1Ijoia2F6YWswZmYiLCJhIjoiY2xnYWt1cm5iMWlnNzNlcWxkcTZ5Z2I2byJ9.goVgwvhiiqQUUWNw0RggQA",
)

# Securing Session data
SECRET_KEY = get_env_variable("SECRET_KEY")
GLOBAL_ASYNC_QUERIES_JWT_SECRET = get_env_variable("SECRET_KEY")

# This will make sure the redirect_uri is properly computed, even with SSL offloading
ENABLE_PROXY_FIX = True

LOGO_TARGET_PATH = "/superset/welcome/"

FEATURE_FLAGS = {
    "ALERT_REPORTS": True,
    "DASHBOARD_RBAC": True,
    "DYNAMIC_PLUGINS": True,
    "ENABLE_TEMPLATE_PROCESSING": True,
    "GLOBAL_ASYNC_QUERIES": False,
    "SQLLAB_BACKEND_PERSISTENCE": True,
    "ENABLE_JAVASCRIPT_CONTROLS": True,
    "DASHBOARD_CROSS_FILTERS": True,
    "RLS_IN_SQLLAB": True,
    "DRILL_TO_DETAIL": True,
    "TAGGING_SYSTEM": True,
    "DRILL_BY": True,
    "ALLOW_FULL_CSV_EXPORT": False,
    "DASHBOARD_NATIVE_FILTERS_SET": True,
    "SQLLAB_FORCE_RUN_ASYNC": False,
}

LANGUAGES = {
    "ru": {"flag": "ru", "name": "Russian"},
    "en": {"flag": "gb", "name": "English"},
}

# Disable SWAGGER UI for production
FAB_API_SWAGGER_UI = True

# Set this to false if you don't want users to be able to request/grant
# datasource access requests from/to other users.
ENABLE_ACCESS_REQUEST = True

KAFKA_TOPIC = "superset.log.v1"
# https://github.com/Azure/azure-event-hubs-for-kafka/blob/ca9afcac342f0aed5170705516110662f37d4085/quickstart/python/producer.py#L22
# from here took the config
KAFKA_CONFIG = {
    "bootstrap.servers": get_env_variable("KAFKA_BOOTSTRAP", "test"),
    "security.protocol": "SASL_SSL",
    "sasl.mechanism": "PLAIN",
    "sasl.username": "$ConnectionString",
    "sasl.password": get_env_variable("KAFKA_CONNECT_STRING", "test"),
}

# Kafka consumer configuration
KAFKA_CONSUMER_CONFIG = {
    "bootstrap.servers": get_env_variable("KAFKA_BOOTSTRAP_CONSUMER", "test"),
    "security.protocol": "SASL_SSL",
    "sasl.mechanism": "PLAIN",
    "sasl.username": "$ConnectionString",
    "sasl.password": get_env_variable("KAFKA_CONNECT_STRING_CONSUMER", "test"),
    "group.id": "superset-consumer",
    "auto.offset.reset": "latest",
    "enable.auto.commit": True,
}

ORGSTRUCTURE_KAFKA_TOPIC = "orgstructure"

# Import all configuration modules
superset_config_path = os.path.dirname(__file__)
modules = [
    name
    for name in os.listdir(superset_config_path)
    if os.path.isdir(f"{superset_config_path}/{name}")
]
try:
    for module in modules:
        additional_configs = importlib.import_module(module)
        this_module = sys.modules[__name__]
        logger.warning("module: %s", this_module)

        for key in dir(additional_configs):
            if key.isupper():
                setattr(this_module, key, getattr(additional_configs, key))
except Exception:
    logger.exception("Failed to import config for %s=%s")
    raise

TIME_GRAIN_DENYLIST: list[str] = [
    "PT1S",
    "PT5S",
    "PT30S",
    "PT1M",
    "PT5M",
    "PT10M",
    "PT15M",
    "PT30M",
    "PT1H",
    "PT6H",
    "P1W",
    "P1W/1970-01-03T00:00:00Z",
    "P1W/1970-01-04T00:00:00Z",
]

CSV_EXPORT = {"encoding": "utf-8", "sep": ";"}

EXCEL_EXPORT = {"index": False}

SUPERSET_WEBSERVER_TIMEOUT = int(timedelta(minutes=2).total_seconds())
PERMANENT_SESSION_LIFETIME = int(timedelta(hours=10).total_seconds())

HTML_SANITIZATION = False

# Enable profiling of Python calls. Turn this on and append ``?_instrument=1``
# to the page to see the call stack.
PROFILING = True

TALISMAN_DEV_CONFIG = {
    "content_security_policy": {
        "default-src": ["'self'"],
        "img-src": ["'self'", "data:"],
        "worker-src": ["'self'", "blob:"],
        "connect-src": [
            "'self'",
            "https://api.mapbox.com",
            "https://events.mapbox.com",
            "https://api.rollbar.com",
        ],
        "object-src": "'none'",
        "style-src": [
            "'self'",
            "'unsafe-inline'",
            "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        ],
        "script-src": [
            "'self'",
            "'unsafe-inline'",
            "'unsafe-eval'",
        ],
    },
    "content_security_policy_nonce_in": ["script-src"],
    "force_https": False,
}

CONTENT_SECURITY_POLICY = {
    "default-src": ["'self'"],
    "script-src": [
        "'self'",
        "'unsafe-inline'",  # Required for analytics initialization
        "'unsafe-eval'",
    ],
    "style-src": ["'self'", "'unsafe-inline'"],
    "img-src": ["'self'", "data:"],
    "connect-src": [
        "'self'",
        "https://api.mapbox.com",
        "https://events.mapbox.com",
        "https://api.rollbar.com",
    ],
    "font-src": ["'self'", "data:"],
    "frame-src": [
        "'self'",
    ],
}

TALISMAN_CONFIG = {
    "content_security_policy": {
        "default-src": ["'self'"],
        "img-src": ["'self'", "data:"],
        "worker-src": ["'self'", "blob:"],
        "connect-src": [
            "'self'",
            "https://api.mapbox.com",
            "https://events.mapbox.com",
            "https://api.rollbar.com",
        ],
        "object-src": "'none'",
        "style-src": [
            "'self'",
            "'unsafe-inline'",
            "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        ],
        "script-src": [
            "'self'",
            "'strict-dynamic'",
            "'unsafe-eval'",
        ],
    },
    "content_security_policy_nonce_in": ["script-src"],
    "force_https": False,
}


def FLASK_APP_MUTATOR(app):
    """Add root routes for handling /robots.txt, /favicon.ico, etc."""
    from flask import redirect, Response

    @app.route("/robots.txt")
    def robots_txt():
        """Serve robots.txt file"""
        robots_content = """User-agent: *
Disallow: /api/
Disallow: /superset/explore/
Disallow: /superset/dashboard/
Allow: /
"""
        return Response(robots_content, mimetype="text/plain")

    @app.route("/favicon.ico")
    def favicon():
        """Serve favicon.ico from static assets"""
        return redirect("/static/assets/images/favicon.png")

    @app.route("/apple-touch-icon.png")
    @app.route("/apple-touch-icon-precomposed.png")
    def apple_touch_icon():
        """Serve apple touch icon from static assets"""
        return redirect("/static/assets/images/favicon.png")


# Development environment overrides
if is_development():
    from superset_dev_config import *

    logger.info("Development environment was set.")
