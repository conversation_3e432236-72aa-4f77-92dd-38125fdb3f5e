import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';

const StyledButton = styled.button<{ disabled?: boolean }>`
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
`;

const ActionButtons = ({
  onAddFolder,
  onSave,
  hasChanges,
}: {
  onAddFolder?: () => void;
  onSave?: () => void;
  hasChanges?: boolean;
}) => (
  <div className="action-buttons">
    <span
      role="button"
      aria-label={t('Add folder')}
      tabIndex={0}
      onClick={onAddFolder}
    >
      <Icons.FolderAddOutlined iconSize="xl" />
    </span>
    <StyledButton
      type="button"
      aria-label={t('Save')}
      onClick={onSave}
      disabled={!hasChanges}
    >
      <Icons.SaveOutlined iconSize="xl" />
    </StyledButton>
  </div>
);

export default ActionButtons;
