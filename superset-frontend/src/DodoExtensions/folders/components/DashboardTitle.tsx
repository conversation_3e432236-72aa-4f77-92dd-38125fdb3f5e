import { Link, NavLink } from 'react-router-dom';
import { styled, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import Icons from 'src/components/Icons';
import HighlightedText from './HighlightedText';
import { Dashboard } from '../types';

const locale = bootstrapData?.common?.locale || 'en';

const StyledNavLink = styled(NavLink)<{ businessId: string }>`
  text-decoration: none !important;
  color: ${({ theme }) => theme.colors.grayscale.base};
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background: ${({ theme }) => theme.colors.grayscale.light4};
    color: ${({ theme }) => theme.colors.grayscale.base};
    text-decoration: none;
  }

  &.active-link-${props => props.businessId} {
    background: ${({ theme }) => theme.colors.primary.light4};
    color: ${({ theme }) => theme.colors.primary.base};
  }
`;

const DashboardTitle = ({
  editMode,
  data,
  onDelete,
  searchTerm,
  active,
  showCertification = true,
}: {
  editMode: boolean;
  data: Dashboard;
  onDelete?: (dashboard: Dashboard) => void;
  searchTerm?: string;
  active?: boolean;
  showCertification?: boolean;
}) => {
  const title =
    locale === 'ru'
      ? data.name_ru || data.name_en
      : data.name_en || data.name_ru;

  return editMode ? (
    <div className="item-title dashboard">
      <span>
        <HighlightedText text={title} searchTerm={searchTerm} />
      </span>
      {onDelete && (
        <span
          role="button"
          tabIndex={0}
          className="dash-delete-icon"
          onClick={e => {
            e.stopPropagation();
            onDelete(data);
          }}
          aria-label={t('Delete dashboard')}
        >
          <Icons.Trash iconSize="m" />
        </span>
      )}
    </div>
  ) : (
    <NavLink to={`/superset/dashboard/${data.id}`} style={{ color: 'inherit' }}>
      {data.certified_by && showCertification && (
        <Icons.Certified className="certified-icon" iconSize="m" />
      )}
      <HighlightedText text={title} searchTerm={searchTerm} />
    </NavLink>
  );
};

export default DashboardTitle;
