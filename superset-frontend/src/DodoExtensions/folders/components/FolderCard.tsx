import { useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { Tree, TreeNodeProps } from 'antd-v5';
import { SupersetClient, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import Icons from 'src/components/Icons';
import { Input } from 'src/components/Input';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import Loading from 'src/components/Loading';
import {
  Entity,
  isFolderType,
  TreeNodeData,
  Folder,
  Dashboard,
  EntityType,
  FolderSlug,
  FolderLevel,
} from '../types';
import {
  buildTreeData,
  filterTreeData,
  removeEntityFromData,
  updateFolderInData,
} from '../utils';
import FolderModal from './FolderModal';
import FolderTitle from './FolderTitle';
import DeleteConfirmModal from './DeleteConfirmModal';
import { useDragAndDrop } from '../hooks/useDragAndDrop';
import DashboardTitle from './DashboardTitle';
import StyledCard from '../styles';
import ActionButtons from './ActionButtons';

const locale = bootstrapData?.common?.locale || 'en';

const BrowseLink = ({ to }: { to: string | undefined }) =>
  to ? (
    <Link to={to} className="browse-link">
      <span>{t('Browse dashboards')}</span>
      <Icons.ArrowRightOutlined iconSize="m" />
    </Link>
  ) : (
    <></>
  );

interface IProps {
  editMode: boolean;
  canEdit?: boolean;
  title: string;
  slug: FolderSlug | null;
  rootFolderId: number;
  content: Entity[];
  setContent?: (data: Entity[]) => void;
  browse?: string;
  loading: boolean;
  canSave?: boolean;
  setSelectedFolderData?: React.Dispatch<
    React.SetStateAction<{
      id: number;
      dashboards: Set<number>;
    } | null>
  >;
  selectedfolderId?: number;
  currentContentMap?: Record<number, Entity[]>;
  updateInitialContentMap?: (
    folderId: number | null,
    action: 'add' | 'remove' | 'update',
    entities: Entity[],
  ) => void;
  updateCurrentContentMap?: (
    folderId: number | null,
    action: 'add' | 'remove' | 'update',
    entities: Entity[],
  ) => void;
  handleSave?: () => void;
}

const FolderCard = ({
  title,
  slug,
  rootFolderId,
  browse,
  canEdit,
  content,
  setContent = () => {},
  editMode,
  canSave,
  loading,
  setSelectedFolderData = () => {},
  selectedfolderId,
  currentContentMap = {},
  updateInitialContentMap = () => {},
  updateCurrentContentMap = () => {},
  handleSave = () => {},
}: IProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [addFolderModalVisible, setAddFolderModalVisible] = useState(false);
  const [folderToEdit, setFolderToEdit] = useState<Folder | null>(null);
  const [dashboardToDelete, setDashboardToDelete] = useState<Dashboard | null>(
    null,
  );
  const { addSuccessToast, addDangerToast } = useToasts();

  const updateSelectedFolderData = (updatedFolder: Folder) => {
    setSelectedFolderData?.(prev => {
      if (!prev || prev.id !== updatedFolder.id) return prev;
      return {
        ...prev,
        dashboards: new Set(
          updatedFolder.children
            .filter((child: Entity) => child.item_type === EntityType.Dashboard)
            .map((child: Entity) => child.id),
        ),
      };
    });
  };

  const { onDrop, allowDrop } = useDragAndDrop(
    content,
    rootFolderId,
    addDangerToast,
    setContent,
    updateCurrentContentMap,
    updateSelectedFolderData,
  );

  const stopSearching = () => {
    setSearchTerm('');
    searchInputRef.current!.blur();
  };

  const handleDeleteFolder = async (folder: Folder) => {
    try {
      await SupersetClient.delete({
        endpoint: `/api/v1/folder/${folder.id}`,
      });

      setContent(removeEntityFromData(content, EntityType.Folder, folder.id));

      updateInitialContentMap(folder.parent_id, 'remove', [folder]);
      updateCurrentContentMap(folder.parent_id, 'remove', [folder]);

      setFolderToEdit(null);

      if (selectedfolderId === folder.id) {
        setSelectedFolderData?.(null);
      }

      addSuccessToast(t('Folder successfully deleted.'));
    } catch (error) {
      addDangerToast(t('Failed to delete folder.'));
    }
  };

  const handleConfirmDeleteDashboard = () => {
    if (!dashboardToDelete) return;

    setContent(
      removeEntityFromData(content, EntityType.Dashboard, dashboardToDelete.id),
    );

    updateCurrentContentMap(dashboardToDelete.parent_id, 'remove', [
      dashboardToDelete,
    ]);

    setSelectedFolderData?.(prev => {
      if (!prev) return null;
      return {
        ...prev,
        dashboards: new Set(
          Array.from(prev.dashboards).filter(id => id !== dashboardToDelete.id),
        ),
      };
    });

    setDashboardToDelete(null);
    addSuccessToast(t('Dashboard deleted. Click Save to apply changes.'));
  };

  const handleEditFolder = async (updatedFolder: Folder) => {
    try {
      const payload = {
        name_ru: updatedFolder.name_ru,
        name_en: updatedFolder.name_en,
        description_ru: updatedFolder.description_ru,
        description_en: updatedFolder.description_en,
      };
      const response = await SupersetClient.put({
        endpoint: `/api/v1/folder/${updatedFolder.id}`,
        jsonPayload: payload,
      });

      const responseFolder = response.json.folder;
      const resultFolder = {
        ...responseFolder,
        children: updatedFolder.children,
      };
      setContent(updateFolderInData(content, resultFolder));

      addSuccessToast(t('Folder updated successfully'));
    } catch (error) {
      addDangerToast(`${t('Failed to edit folder')}: ${error?.message}`);
    }
  };

  const handleAddFolder = async (newFolder: Folder) => {
    try {
      const payload = {
        name_ru: newFolder.name_ru,
        name_en: newFolder.name_en,
        description_ru: newFolder.description_ru,
        description_en: newFolder.description_en,
        parent_id: newFolder.parent_id,
      };
      const response = await SupersetClient.post({
        endpoint: `/api/v1/folder/`,
        jsonPayload: payload,
      });
      const parentFolder = response.json.folder;

      const createdFolder =
        parentFolder.children[parentFolder.children.length - 1];

      updateInitialContentMap(parentFolder.id, 'add', [createdFolder]);
      updateCurrentContentMap(parentFolder.id, 'add', [createdFolder]);

      if (parentFolder.id === rootFolderId) {
        setContent([...content, createdFolder]);
      } else {
        const resultFolder = {
          ...parentFolder,
          children: [...currentContentMap[parentFolder.id], createdFolder],
        };
        setContent(updateFolderInData(content, resultFolder));
      }

      addSuccessToast(t('Folder added successfully.'));
    } catch (error) {
      addDangerToast(t('Failed to add folder.'));
    }
  };

  const titleNode =
    canEdit && slug ? (
      <span className="header-title">
        <Link to={`/folder/${slug}`}>
          <Icons.EditOutlined iconSize="l" />
        </Link>
        <span>{title}</span>
      </span>
    ) : (
      title
    );

  if (loading) {
    return (
      <StyledCard title={titleNode}>
        <Loading position="inline-centered" />
      </StyledCard>
    );
  }

  const extra = editMode ? (
    <ActionButtons
      onAddFolder={() => setAddFolderModalVisible(true)}
      onSave={handleSave}
      hasChanges={canSave}
    />
  ) : (
    <BrowseLink to={browse} />
  );

  const treeData = buildTreeData(content, locale, editMode);
  const filteredTreeData = filterTreeData(treeData, searchTerm);

  return (
    <StyledCard title={titleNode} extra={extra}>
      <h3 className="search-title">{t('Search for folders and dashboards')}</h3>
      <Input
        type="text"
        ref={searchInputRef as any}
        placeholder={t('Search')}
        value={searchTerm}
        className="search-input"
        onChange={event => setSearchTerm(event.target.value)}
        prefix={
          <div className="search-icon">
            <Icons.Search iconSize="l" />
          </div>
        }
        suffix={
          <div className="search-icon">
            {searchTerm && (
              <Icons.XLarge iconSize="m" onClick={stopSearching} />
            )}
          </div>
        }
      />
      {content.length > 0 && (
        <>
          <div className="tree-container">
            <Tree
              treeData={filteredTreeData}
              titleRender={nodeData => {
                const { entity, searchTerm } = nodeData as TreeNodeData;

                const titleContent = isFolderType(entity) ? (
                  <FolderTitle
                    editMode={editMode}
                    folder={entity}
                    onEdit={() => setFolderToEdit(entity)}
                    searchTerm={searchTerm}
                  />
                ) : (
                  <DashboardTitle
                    editMode={editMode}
                    data={entity}
                    onDelete={editMode ? setDashboardToDelete : undefined}
                    searchTerm={searchTerm}
                  />
                );
                return titleContent;
              }}
              switcherIcon={<Icons.CaretDownOutlined />}
              icon={(props: TreeNodeProps & TreeNodeData) => {
                if (props.data.entity.item_type === EntityType.Dashboard) {
                  return <Icons.FundViewOutlined iconSize="m" />;
                }
                return props.expanded ? (
                  <Icons.FolderOpenOutlined iconSize="m" />
                ) : (
                  <Icons.FolderOutlined iconSize="m" />
                );
              }}
              draggable={(props: TreeNodeData) => {
                if (!editMode) return false;
                if (props.entity.item_type === EntityType.Folder) {
                  return props.entity.level === FolderLevel.User;
                }
                return true;
              }}
              onDrop={onDrop}
              allowDrop={allowDrop}
              className="tree"
              showLine={!editMode}
              showIcon
              onSelect={
                setSelectedFolderData
                  ? (_, info) => {
                      setSelectedFolderData(
                        info.selected
                          ? {
                              // @ts-ignore
                              id: info.node.id,
                              dashboards: new Set(
                                // @ts-ignore
                                (info.node.entity.children || [])
                                  .filter(
                                    (child: Entity) =>
                                      child.item_type === EntityType.Dashboard,
                                  )
                                  .map((child: Entity) => child.id),
                              ),
                            }
                          : null,
                      );
                    }
                  : undefined
              }
              defaultExpandAll
            />
          </div>

          {/* Modals */}
          <FolderModal
            mode="edit"
            show={Boolean(folderToEdit)}
            folder={folderToEdit}
            onExit={() => setFolderToEdit(null)}
            onSuccess={handleEditFolder}
            onDelete={() => handleDeleteFolder(folderToEdit!)}
          />

          <FolderModal
            mode="add"
            show={addFolderModalVisible}
            rootFolderId={rootFolderId}
            availableFolders={content}
            onExit={() => setAddFolderModalVisible(false)}
            onSuccess={handleAddFolder}
          />

          <DeleteConfirmModal
            show={Boolean(dashboardToDelete)}
            dashboard={dashboardToDelete}
            onConfirm={handleConfirmDeleteDashboard}
            onCancel={() => setDashboardToDelete(null)}
          />
        </>
      )}
    </StyledCard>
  );
};

export default FolderCard;
