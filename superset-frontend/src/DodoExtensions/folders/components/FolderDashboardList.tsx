import { useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { isFeatureEnabled, FeatureFlag, t } from '@superset-ui/core';
import { createFetchRelated, createErrorHand<PERSON> } from 'src/views/CRUD/utils';
import { useListViewResource } from 'src/views/CRUD/hooks';
import { TagsList } from 'src/components/Tags';
import { bootstrapData } from 'src/preamble';
import IndeterminateCheckbox from 'src/components/IndeterminateCheckbox';

import ListView, {
  Filter,
  Filters,
  FilterOperator,
  ListViewProps,
} from 'src/components/ListView';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import { findPermission } from 'src/utils/findPermission';
import { UserWithPermissionsAndRoles } from 'src/types/bootstrapTypes';
import { loadTags } from 'src/components/Tags/utils';
import FacePile from 'src/components/FacePile';
import Tag from 'src/types/TagType';
import { Dashboard } from 'src/pages/DashboardList';
import { FolderListDashboard } from '../types';

const locale = bootstrapData?.common?.locale || 'en';

const PAGE_SIZE = 15;
const DASHBOARD_COLUMNS_TO_FETCH = [
  'id',
  'dashboard_title',
  'dashboard_title_ru',
  'url',
  'owners.id',
  'owners.first_name',
  'owners.last_name',
  'owners',
  'tags.id',
  'tags.name',
  'tags.type',
  'certified_by',
  'certification_details',
];
const initialSort = [{ id: 'changed_on_delta_humanized', desc: true }];

const FolderDashboardList = ({
  addDashboardsToFolder,
  selectedFolderData,
}: {
  addDashboardsToFolder: (dashboards: FolderListDashboard[]) => void;
  selectedFolderData: { id: number; dashboards: Set<number> } | null;
}) => {
  const { addDangerToast, addSuccessToast } = useToasts();

  const {
    state: {
      loading,
      resourceCount: dashboardCount,
      resourceCollection: dashboards,
      bulkSelectEnabled,
    },
    fetchData,
    refreshData,
  } = useListViewResource<Dashboard>(
    'dashboard',
    t('dashboard'),
    addDangerToast,
    undefined,
    undefined,
    undefined,
    undefined,
    DASHBOARD_COLUMNS_TO_FETCH,
    true,
  );

  const user = useSelector<any, UserWithPermissionsAndRoles>(
    state => state.user,
  );
  const { roles } = user;
  const canReadTag = findPermission('can_read', 'Tag', roles);

  const customBulkSelectColumnConfig = useMemo(
    () => ({
      Cell: ({ row }: any) => {
        const dashboardId = row.original.id;
        const isInFolder =
          selectedFolderData?.dashboards.has(dashboardId) || false;

        return (
          <IndeterminateCheckbox
            {...row.getToggleRowSelectedProps()}
            id={row.id}
            checked={isInFolder || row.isSelected}
            onChange={
              isInFolder ? () => {} : row.getToggleRowSelectedProps().onChange
            }
            indeterminate={false}
            disabled={isInFolder}
          />
        );
      },
      Header: ({ getToggleAllRowsSelectedProps }: any) => (
        <IndeterminateCheckbox
          {...getToggleAllRowsSelectedProps()}
          id="header-toggle-all"
        />
      ),
      id: 'selection',
      size: 'sm',
    }),
    [selectedFolderData],
  );

  const columns = useMemo(
    () => [
      {
        Header: 'ID',
        size: 'xs',
        disableSortBy: true,
        accessor: 'id',
      },
      {
        Cell: ({
          row: {
            original: {
              url,
              dashboard_title: dashboardTitle,
              dashboard_title_ru: dashboardTitleRU,
            },
          },
        }: any) => (
          <Link to={url}>
            {locale === 'ru'
              ? dashboardTitleRU || dashboardTitle
              : dashboardTitle}
          </Link>
        ),
        Header: t('Title'),
        accessor: 'dashboard_title',
      },
      {
        accessor: 'published',
        hidden: true,
      },
      {
        Cell: ({
          row: {
            original: { tags = [] },
          },
        }: {
          row: {
            original: {
              tags: Tag[];
            };
          };
        }) => (
          <TagsList
            tags={tags.filter(
              (tag: Tag) => tag.type === 'TagTypes.custom' || tag.type === 1,
            )}
            maxTags={3}
          />
        ),
        Header: t('Tags'),
        accessor: 'tags',
        disableSortBy: true,
        hidden: !isFeatureEnabled(FeatureFlag.TaggingSystem),
      },
      {
        Cell: ({
          row: {
            original: { owners = [] },
          },
        }: any) => <FacePile users={owners} />,
        Header: t('Owners'),
        accessor: 'owners',
        disableSortBy: true,
        size: 'xl',
      },
    ],
    [],
  );

  const favoritesFilter: Filter = useMemo(
    () => ({
      Header: t('Favorite'),
      key: 'favorite',
      id: 'id',
      urlDisplay: 'favorite',
      input: 'select',
      operator: FilterOperator.DashboardIsFav,
      unfilteredLabel: t('Any'),
      selects: [
        { label: t('Yes'), value: true },
        { label: t('No'), value: false },
      ],
    }),
    [],
  );

  const filters: Filters = useMemo(() => {
    const filters_list = [
      {
        Header: t('Name'),
        key: 'search',
        id: 'dashboard_title',
        input: 'search',
        operator: FilterOperator.TitleOrSlug,
      },
      {
        Header: t('Status'),
        key: 'published',
        id: 'published',
        input: 'select',
        operator: FilterOperator.Equals,
        unfilteredLabel: t('Any'),
        selects: [
          { label: t('Published'), value: true },
          { label: t('Draft'), value: false },
        ],
      },
      ...(isFeatureEnabled(FeatureFlag.TaggingSystem) && canReadTag
        ? [
            {
              Header: t('Tag'),
              key: 'tags',
              id: 'tags',
              input: 'select',
              operator: FilterOperator.DashboardTagById,
              unfilteredLabel: t('All'),
              fetchSelects: loadTags,
            },
          ]
        : []),
      {
        Header: t('Owner'),
        key: 'owner',
        id: 'owners',
        input: 'select',
        operator: FilterOperator.RelationManyMany,
        unfilteredLabel: t('All'),
        fetchSelects: createFetchRelated(
          'dashboard',
          'owners',
          createErrorHandler(errMsg =>
            addDangerToast(
              t(
                'An error occurred while fetching dashboard owner values: %s',
                errMsg,
              ),
            ),
          ),
          // @ts-ignore
          user,
        ),
        paginate: true,
      },
      ...(user?.userId ? [favoritesFilter] : []),
    ] as Filters;
    return filters_list;
  }, [addDangerToast, canReadTag, favoritesFilter, user]);

  const bulkActions: ListViewProps['bulkActions'] = [
    {
      key: 'add_to_folder',
      name: t('Add to folder'),
      type: 'primary',
      onSelect: addDashboardsToFolder,
      disabled: !selectedFolderData,
      resetBulkSelectionAfterAction: true,
      tooltipOnDisabled: t('Select a folder to add dashboards to'),
    },
  ];

  return (
    <ListView<Dashboard>
      bulkActions={bulkActions}
      bulkSelectEnabled={bulkSelectEnabled}
      columns={columns}
      count={dashboardCount}
      data={dashboards}
      fetchData={fetchData}
      refreshData={refreshData}
      filters={filters}
      initialSort={initialSort}
      loading={loading}
      pageSize={PAGE_SIZE}
      addSuccessToast={addSuccessToast}
      addDangerToast={addDangerToast}
      className="folder-dashboard-list-view"
      customBulkSelectColumnConfig={customBulkSelectColumnConfig}
    />
  );
};

export default FolderDashboardList;
