import { useEffect, useMemo, useRef, useState } from 'react';
import { Form } from 'antd-v5';
import { TreeSelect } from 'antd';
import { styled, t } from '@superset-ui/core';
import { Input, TextArea } from 'src/components/Input';
import Modal from 'src/components/Modal';
import Button from 'src/components/Button';
import { Entity, EntityType, Folder, FolderLevel, FolderType } from '../types';

const StyledForm = styled(Form)`
  .form-item {
    margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  }

  .language-section {
    margin-bottom: ${({ theme }) => theme.gridUnit * 6}px;

    h4 {
      margin-bottom: ${({ theme }) => theme.gridUnit * 3}px;
      color: ${({ theme }) => theme.colors.grayscale.dark1};
      font-weight: 600;
    }
  }

  .parent-folder-section {
    margin-bottom: ${({ theme }) => theme.gridUnit * 6}px;

    .ant-tree-select {
      width: 100%;
    }
  }
`;

const buildTreeSelectData = (entities: Entity[]): any[] =>
  entities
    .filter(entity => entity.item_type === EntityType.Folder)
    .map(entity => ({
      title: entity.name_ru || entity.name_en,
      value: entity.id,
      key: entity.id,
      children:
        entity.item_type === EntityType.Folder && entity.children
          ? buildTreeSelectData(entity.children)
          : [],
    }));

interface FolderModalProps {
  show: boolean;
  folder?: Folder | null;
  rootFolderId?: number;
  availableFolders?: Entity[];
  mode: 'add' | 'edit';
  onExit: () => void;
  onSuccess: (folder: Folder) => Promise<void>;
  onDelete?: () => Promise<void>;
}

interface FormValues {
  name_ru: string;
  name_en: string;
  description_ru?: string;
  description_en?: string;
  parent_id?: number;
}

const FolderModal: React.FC<FolderModalProps> = ({
  show,
  folder,
  rootFolderId,
  availableFolders = [],
  mode,
  onExit,
  onSuccess,
  onDelete,
}) => {
  const [submitting, setSubmitting] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const initialValuesRef = useRef<FormValues | null>(null);
  const [form] = Form.useForm<FormValues>();
  const currentValues = Form.useWatch([], form);
  const isAddMode = mode === 'add';

  useEffect(() => {
    if (!show) return;

    let values: FormValues;
    if (isAddMode) {
      values = {
        parent_id: rootFolderId,
        name_ru: '',
        name_en: '',
        description_ru: '',
        description_en: '',
      };
    } else if (folder) {
      values = {
        name_ru: folder.name_ru || '',
        name_en: folder.name_en || '',
        description_ru: folder.description_ru || '',
        description_en: folder.description_en || '',
      };
    } else {
      return;
    }

    form.setFieldsValue(values);
    initialValuesRef.current = values;
  }, [show, isAddMode, folder, rootFolderId, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      let resultFolder: Folder;

      if (mode === 'add') {
        resultFolder = {
          id: Date.now(),
          item_type: EntityType.Folder,
          folder_type: FolderType.Personal,
          name_ru: values.name_ru,
          name_en: values.name_en,
          description_ru: values.description_ru || '',
          description_en: values.description_en || '',
          parent_id: values.parent_id ? values.parent_id : 0,
          children: [],
          slug: null,
          level: 0,
        };
      } else {
        resultFolder = {
          ...folder!,
          name_ru: values.name_ru,
          name_en: values.name_en,
          description_ru: values.description_ru || '',
          description_en: values.description_en || '',
        };
      }

      await onSuccess(resultFolder);
      setSubmitting(false);
      onExit();
    } catch (error) {
      console.warn('Error validating form');
    }
  };

  const handleDelete = async () => {
    setDeleting(true);
    await onDelete!();
    setDeleting(false);
    onExit();
  };

  const handleCancel = () => {
    form.resetFields();
    onExit();
  };

  const isValuesChanged = () => {
    const initialValues = initialValuesRef.current;
    if (!initialValues) return false;

    if (isAddMode) {
      return (
        currentValues.name_ru !== '' ||
        currentValues.name_en !== '' ||
        currentValues.parent_id !== undefined
      );
    }

    return (
      currentValues.name_ru !== initialValues.name_ru ||
      currentValues.name_en !== initialValues.name_en ||
      currentValues.description_ru !== initialValues.description_ru ||
      currentValues.description_en !== initialValues.description_en
    );
  };

  const treeSelectData = useMemo(() => {
    if (!isAddMode) {
      return [];
    }

    return [
      {
        title: t('Root Level'),
        value: rootFolderId,
        key: 'root',
      },
      ...buildTreeSelectData(availableFolders),
    ];
  }, [availableFolders, isAddMode, rootFolderId]);

  const footer = (
    <>
      <Button onClick={handleCancel}>{t('Cancel')}</Button>
      {!isAddMode && folder?.level === FolderLevel.User && (
        <Button
          buttonStyle="danger"
          onClick={handleDelete}
          disabled={submitting || deleting}
          loading={deleting}
        >
          {t('Delete')}
        </Button>
      )}
      <Button
        buttonStyle="primary"
        onClick={handleSubmit}
        disabled={!isValuesChanged() || submitting || deleting}
        loading={submitting}
      >
        {isAddMode ? t('Create') : t('Save')}
      </Button>
    </>
  );

  return (
    <Modal
      title={isAddMode ? t('Add New Folder') : t('Edit Folder')}
      show={show}
      onHide={handleCancel}
      onHandledPrimaryAction={handleSubmit}
      footer={footer}
      width="600px"
      responsive
    >
      <StyledForm form={form} layout="vertical" requiredMark={false}>
        {isAddMode && (
          <div className="parent-folder-section">
            <Form.Item
              name="parent_id"
              label={t('Parent Folder')}
              className="form-item"
            >
              <TreeSelect
                placeholder={t('Select parent folder')}
                treeData={treeSelectData}
                treeDefaultExpandAll
                allowClear
              />
            </Form.Item>
          </div>
        )}

        <div className="language-section">
          <h4>{t('Russian')}</h4>
          <Form.Item
            name="name_ru"
            label={t('Title')}
            className="form-item"
            rules={[
              {
                required: true,
                message: t('Please enter folder title in Russian'),
              },
              {
                max: 500,
                message: t('Name must be less than 500 characters'),
              },
            ]}
          >
            <Input placeholder={t('Enter folder title in Russian')} />
          </Form.Item>

          <Form.Item
            name="description_ru"
            label={t('Description')}
            className="form-item"
          >
            <TextArea
              rows={3}
              placeholder={t('Enter folder description in Russian')}
            />
          </Form.Item>
        </div>

        <div className="language-section">
          <h4>{t('English')}</h4>
          <Form.Item
            name="name_en"
            label={t('Title')}
            className="form-item"
            rules={[
              {
                required: true,
                message: t('Please enter folder title in English'),
              },
              {
                max: 500,
                message: t('Name must be less than 500 characters'),
              },
            ]}
          >
            <Input placeholder={t('Enter folder title in English')} />
          </Form.Item>

          <Form.Item
            name="description_en"
            label={t('Description')}
            className="form-item"
          >
            <TextArea
              rows={3}
              placeholder={t('Enter folder description in English')}
            />
          </Form.Item>
        </div>
      </StyledForm>
    </Modal>
  );
};

export default FolderModal;
