import { bootstrapData } from 'src/preamble';
import { css, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import InfoTooltip from 'src/components/InfoTooltip';
import HighlightedText from './HighlightedText';
import { Folder, FolderLevel } from '../types';

const locale = bootstrapData?.common?.locale || 'en';

const FolderTitle = ({
  editMode,
  onEdit,
  folder,
  searchTerm,
}: {
  editMode: boolean;
  onEdit?: (folder: Folder) => void;
  folder: Folder;
  searchTerm?: string;
}) => {
  const title =
    locale === 'ru'
      ? folder.name_ru || folder.name_en
      : folder.name_en || folder.name_ru;

  const description =
    locale === 'ru'
      ? folder.description_ru || folder.description_en
      : folder.description_en || folder.description_ru;

  return (
    <div className="item-title">
      <span
        css={css`
          display: flex;
          align-items: center;
          gap: 2px;
        `}
      >
        <HighlightedText text={title} searchTerm={searchTerm} />
        {description && (
          <InfoTooltip
            tooltip={description}
            placement="top"
            iconStyle={{ fontSize: '20px' }}
          />
        )}
      </span>
      {editMode && folder.level === FolderLevel.User && (
        <span
          role="button"
          tabIndex={0}
          title={t('Edit folder')}
          aria-label={t('Edit folder')}
          onClick={e => {
            e.stopPropagation();
            if (onEdit) {
              onEdit(folder);
            }
          }}
        >
          <Icons.EditOutlined iconSize="m" />
        </span>
      )}
    </div>
  );
};

export default FolderTitle;
