import { styled } from '@superset-ui/core';

interface HighlightedTextProps {
  text: string;
  searchTerm?: string;
  highlightStyle?: React.CSSProperties;
}

const StyledSpan = styled.span`
  ${({ theme }) => `
    background-color: ${theme.colors.alert.light1};
    padding: 1px 2px;
    border-radius: 2px;
  `}
`;

export const HighlightedText: React.FC<HighlightedTextProps> = ({
  text,
  searchTerm,
}) => {
  if (!searchTerm || !text) {
    return <span>{text}</span>;
  }

  const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');
  const parts = text.split(regex);

  return (
    <span>
      {parts.map((part, index) => {
        const isMatch = regex.test(part);
        regex.lastIndex = 0;

        return isMatch ? (
          <StyledSpan key={index}>{part}</StyledSpan>
        ) : (
          <span key={index}>{part}</span>
        );
      })}
    </span>
  );
};

export default HighlightedText;
