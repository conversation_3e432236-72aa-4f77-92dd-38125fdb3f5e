import { useEffect, useState } from 'react';
import { API_HANDLER, SupersetClient } from '@superset-ui/core';
import { Resource, ResourceStatus } from 'src/hooks/apiResources/apiResources';
import { Folder, FolderSlug } from '../types';

const isStandalone = process.env.type === undefined;

export const useFolder = (
  slug: FolderSlug,
  canFetch = true,
): Resource<Folder> => {
  const [folder, setFolder] = useState<Folder | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchFolder = async () => {
      if (!canFetch) {
        return;
      }
      setLoading(true);
      try {
        const request = async () => {
          if (isStandalone) {
            return SupersetClient.get({
              endpoint: `/api/v1/folder/${slug}`,
            });
          }
          return API_HANDLER.SupersetClient({
            method: 'get',
            url: `/api/v1/folder/${slug}`,
          });
        };
        const response = await request().then(r => (isStandalone ? r.json : r));
        const { folder } = response;
        setFolder(folder);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };
    fetchFolder();
  }, [slug, canFetch]);

  if (loading) {
    return {
      status: ResourceStatus.Loading,
      result: null,
      error: null,
    };
  }

  if (error) {
    return {
      status: ResourceStatus.Error,
      result: null,
      error,
    };
  }

  return {
    result: folder as Folder,
    status: ResourceStatus.Complete,
    error: null,
  };
};
