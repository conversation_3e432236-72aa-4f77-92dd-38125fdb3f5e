import { styled } from '@superset-ui/core';
import Card from 'src/components/Card';

// eslint-disable-next-line theme-colors/no-literal-colors
const StyledCard = styled(Card)`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};

  .antd5-card-head {
    min-height: 54px;
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;

    svg {
      color: ${({ theme }) => theme.colors.primary.base};
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    color: ${({ theme }) => theme.colors.primary.base};
  }

  .browse-link {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;
    line-height: 1rem;
    color: ${({ theme }) => theme.colors.primary.base} !important;

    a {
      color: ${({ theme }) => theme.colors.primary.base} !important;
    }

    span {
      height: 14px;
    }
  }

  .antd5-card-body {
    display: flex;
    flex-direction: column;
    padding: ${({ theme }) => theme.gridUnit * 5}px
      ${({ theme }) => theme.gridUnit * 6}px;
    flex: 1;
  }

  .search-title {
    margin: 0;
    color: ${({ theme }) => theme.colors.grayscale.base};
    font-size: ${({ theme }) => theme.typography.sizes.s}px;
    font-weight: ${({ theme }) => theme.typography.weights.normal};
    text-transform: uppercase;
    line-height: 1.4;
  }

  .search-input {
    margin-top: ${({ theme }) => theme.gridUnit}px;
    border-color: #d9d9d9 !important;
  }

  .tree-container {
    margin-top: ${({ theme }) => theme.gridUnit * 5}px;
    overflow-y: auto;
    max-height: 100%;
    flex: 1;
  }

  .tree {
    background-color: transparent;
  }

  .antd5-tree-treenode,
  .antd5-tree-treenode-motion,
  .antd5-tree-title {
    width: 100%;
  }

  .antd5-tree-node-content-wrapper,
  antd5-tree-node-content-wrapper-normal {
    display: flex;
    flex: 1;
  }

  .antd5-tree-node-content-wrapper:has(.dashboard) {
    &:hover {
      background-color: ${({ theme }) => theme.colors.grayscale.light5};
    }
  }

  .search-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    color: ${({ theme }) => theme.colors.grayscale.light1};
  }

  .item-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    flex: 1;

    span[role='button'] {
      line-height: 14px;
    }

    .dash-delete-icon {
      margin-right: ${({ theme }) => theme.gridUnit * 5}px;
      color: ${({ theme }) => theme.colors.error.base};
    }
  }

  .certified-icon {
    color: ${({ theme }) => theme.colors.primary.base};
  }

  .antd5-tree-treenode.dragging {
    opacity: 0.5;
  }
`;

export default StyledCard;
