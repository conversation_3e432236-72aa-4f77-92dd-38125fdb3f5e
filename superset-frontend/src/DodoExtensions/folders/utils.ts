import {
  Entity,
  EntityType,
  Folder,
  isFolderType,
  TreeNodeData,
} from './types';

export interface HighlightInfo {
  text: string;
  searchTerm: string;
  hasMatch: boolean;
}

export const createHighlightInfo = (
  text: string,
  searchTerm: string,
): HighlightInfo => ({
  text,
  searchTerm,
  hasMatch: searchTerm
    ? text.toLowerCase().includes(searchTerm.toLowerCase())
    : false,
});

export const buildTreeData = (
  entities: Entity[],
  locale: string,
  editMode: boolean,
): TreeNodeData[] =>
  entities.map((entity, index) => ({
    key: `${entity.item_type}-${entity.id}-${entity.parent_id}-${index}`,
    title:
      locale === 'ru'
        ? entity.name_ru || entity.name_en
        : entity.name_en || entity.name_ru,
    entity,
    id: entity.id,
    item_type: entity.item_type,
    selectable: entity.item_type === EntityType.Folder && editMode,
    isLeaf: entity.item_type === EntityType.Dashboard,
    children: isFolderType(entity)
      ? buildTreeData(entity.children || [], locale, editMode)
      : [],
  }));

export const filterTreeData = (
  data: TreeNodeData[],
  searchTerm: string,
): TreeNodeData[] => {
  if (!searchTerm) return data;

  const matchesSearch = (node: TreeNodeData) =>
    node.entity.name_ru?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.entity.name_en?.toLowerCase().includes(searchTerm.toLowerCase());

  const filterNode = (node: TreeNodeData): TreeNodeData | null => {
    if (isFolderType(node.entity) && matchesSearch(node)) {
      return {
        ...node,
        searchTerm,
      };
    }

    if (isFolderType(node.entity)) {
      const filteredChildren = node.children
        ?.map(filterNode)
        .filter(Boolean) as TreeNodeData[];

      if (filteredChildren.length > 0) {
        return {
          ...node,
          searchTerm,
          children: filteredChildren,
        };
      }
      return null;
    }

    if (matchesSearch(node)) {
      return {
        ...node,
        searchTerm,
      };
    }

    return null;
  };

  return data.map(filterNode).filter(Boolean) as TreeNodeData[];
};

export const removeEntityFromData = (
  entities: Entity[],
  entityType: EntityType,
  entityId: number,
): Entity[] =>
  entities
    .filter(
      entity => !(entity.item_type === entityType && entity.id === entityId),
    )
    .map(entity => {
      if (entity.item_type === EntityType.Folder && entity.children) {
        return {
          ...entity,
          children: removeEntityFromData(entity.children, entityType, entityId),
        };
      }
      return entity;
    });

export const updateFolderInData = (
  entities: Entity[],
  updatedFolder: Folder,
): Entity[] =>
  entities.map(entity => {
    if (
      entity.id === updatedFolder.id &&
      entity.item_type === EntityType.Folder
    ) {
      return updatedFolder;
    }
    if (entity.item_type === EntityType.Folder && entity.children) {
      return {
        ...entity,
        children: updateFolderInData(entity.children, updatedFolder),
      };
    }
    return entity;
  });

export const findFolderById = (
  entities: Entity[],
  folderId: number,
): Folder | null => {
  for (let i = 0; i < entities.length; i += 1) {
    const entity = entities[i];

    if (entity.id === folderId && entity.item_type === EntityType.Folder) {
      return entity;
    }

    if (entity.item_type === EntityType.Folder && entity.children) {
      const found = findFolderById(entity.children, folderId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

export const reduceContentMap = (
  contentMap: Record<number, Entity[]>,
  targetFolderId: number,
  action: 'add' | 'remove' | 'update',
  entities: Entity[],
) => {
  if (action === 'add') {
    return {
      ...contentMap,
      [targetFolderId]: [...contentMap[targetFolderId], ...entities],
    };
  }
  if (action === 'remove') {
    const entityToDelete = entities[0];

    return {
      ...contentMap,
      [targetFolderId]: contentMap[targetFolderId].filter(
        item =>
          !(
            item.id === entityToDelete.id &&
            item.item_type === entityToDelete.item_type
          ),
      ),
    };
  }
  return {
    ...contentMap,
    [targetFolderId]: entities,
  };
};
