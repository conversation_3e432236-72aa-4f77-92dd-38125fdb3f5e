import { styled } from '@superset-ui/core';

// eslint-disable-next-line theme-colors/no-literal-colors
export const RootWrapper = styled.div`
  position: relative;
  display: flex;
  min-height: 100vh;
  background: #fff;
`;

export const MainWrapper = styled.div<{ withNavigation: boolean }>`
  position: relative;
  padding-top: ${({ theme, withNavigation }) =>
    withNavigation ? theme.gridUnit * 5 : 0}px;
  flex: 1;

  .loader-wrapper {
    position: relative;
    min-height: 100vh;
  }
`;
