// DODO added

import { BusinessId } from '../types/global';

export const DODOPIZZA_DEFAULT_DASHBOARD_ID = 209;
export const DRINKIT_DEFAULT_DASHBOARD_ID = 507;

type Params = {
  businessId: BusinessId;
  routes: (number | string)[];
};

export const getDefaultDashboard = ({ businessId, routes }: Params) => {
  const firstDashboard =
    routes.length > 0 ? routes.at(0) ?? undefined : undefined;

  switch (businessId) {
    case 'dodopizza':
      if (routes.find(item => item === DODOPIZZA_DEFAULT_DASHBOARD_ID)) {
        return DODOPIZZA_DEFAULT_DASHBOARD_ID;
      }
      break;
    case 'drinkit':
      if (routes.find(item => item === DRINKIT_DEFAULT_DASHBOARD_ID)) {
        return DRINKIT_DEFAULT_DASHBOARD_ID;
      }
      break;
    default:
      return undefined;
  }

  return firstDashboard;
};
