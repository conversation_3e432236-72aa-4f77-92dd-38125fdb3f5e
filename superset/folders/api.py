# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
import logging

from flask import request, Response
from flask_appbuilder.api import expose, protect, safe
from marshmallow import ValidationError

from superset.folders.schemas import openapi_spec_methods_override
from superset.folders.models import FolderLevel, FolderType
from superset.views.base_api import BaseSupersetApi, requires_json
from superset.folders.schemas import FolderCreateSchema, FolderResponseSchema, FolderUpdateSchema, FolderContentItemSchema
from superset.daos.folders import FolderDAO, FolderItemDAO
from superset.utils.core import get_user
from superset.daos.team import TeamDAO
from superset.folders.security import (
    can_update_folder_content,
    can_modify_folder,
    can_modify_folder,
    can_update_folder_content,
)
from superset.folders.utils import calculate_folder_diff, get_folder_tree, reorder_folder_items, validate_folder_content, add_order_to_content
from superset import db

logger = logging.getLogger(__name__)


class FolderRestApi(BaseSupersetApi):
    """
    Folder REST API for managing folders
    """

    resource_name = "folder"
    allow_browser_login = True
    openapi_spec_tag = "Folder"
    openapi_spec_methods = openapi_spec_methods_override

    @expose("/<string:slug>", methods=("GET",))
    @protect()
    @safe
    def get_by_slug(self, slug: str) -> Response:
        user = get_user()
        user_team = TeamDAO.get_team_by_user_id(user.id)

        folder = FolderDAO.find_one_or_none(slug=slug)
        if not folder:
            return self.response_404()

        folder_tree = get_folder_tree(folder, user.id, user_team.id if user_team else None)

        schema = FolderResponseSchema()
        return self.response(200, folder=schema.dump(obj=folder_tree))

    @expose("/", methods=("POST",))
    @protect()
    @safe
    @requires_json
    def create(self) -> Response:
        try:
            folder_schema = FolderCreateSchema()
            folder_data = folder_schema.load(request.json)

            user = get_user()
            user_team = TeamDAO.get_team_by_user_id(user.id)

            parent_folder = FolderDAO.find_by_id(folder_data["parent_id"])
            if not parent_folder:
                return self.response_400(message="Parent folder not found")

            # Validate folder creation permissions
            can_update_folder_content(parent_folder, user)

            # if FolderDAO.find_duplicate(parent_id=folder_data["parent_id"], data=folder_data):
            #     return self.response_400(message="Folder already exists")

            FolderDAO.create(folder_data, parent_folder, user.id, user_team.id if user_team else None)
            db.session.commit()

            folder_tree = get_folder_tree(parent_folder, user.id, user_team.id if user_team else None)

            schema = FolderResponseSchema()
            return self.response(200, folder=schema.dump(obj=folder_tree))
        except ValidationError as error:
            return self.response_400(message=error.messages)
        except Exception as e:
            return self.response_400(message=str(e))

    @expose("/<int:folder_id>", methods=("PUT",))
    @protect()
    @safe
    @requires_json
    def update(self, folder_id: int) -> Response:
        try:
            folder = FolderDAO.find_by_id(folder_id)
            if not folder:
                return self.response_404()

            user = get_user()

            # Validate folder modification permissions
            can_modify_folder(folder, user)

            schema = FolderUpdateSchema()
            data = schema.load(request.json)

            # if FolderDAO.find_duplicate(parent_id=folder.parent_id, data=data):
            #     return self.response_400(message="Folder already exists")

            folder = FolderDAO.update(item=folder, attributes=data)
            db.session.commit()

            schema = FolderResponseSchema()
            return self.response(200, folder=schema.dump(obj=folder))
        except Exception as e:
            return self.response_400(message=str(e))

    @expose("/<int:folder_id>/content", methods=("PUT",))
    @protect()
    @safe
    @requires_json
    def update_content(self, folder_id: int) -> Response:
        try:
            folder = FolderDAO.find_by_id(folder_id)
            if not folder:
                return self.response_404()

            user = get_user()

            # Validate folder content modification permissions
            can_update_folder_content(folder, user)

            user_team = TeamDAO.get_team_by_user_id(user.id)

            child_folders = FolderDAO.get_by_parent_id(folder_id)
            items = FolderItemDAO.get_by_folders([folder_id], user_id=user.id, team_id=user_team.id if user_team else None)

            current_content = FolderContentItemSchema().dump(child_folders + items, many=True)

            new_content = FolderContentItemSchema().load(request.json, many=True)

            if not validate_folder_content(folder, new_content, user):
                return self.response_400(message="Invalid content")

            new_content = add_order_to_content(new_content)

            changes = calculate_folder_diff(current_content, new_content)

            reorder_folder_items(folder_id, changes)

            folder_tree = get_folder_tree(folder, user.id, user_team.id if user_team else None)

            schema = FolderResponseSchema()
            return self.response(200, folder=schema.dump(obj=folder_tree))
        except Exception as e:
            return self.response_400(message=str(e))

    @expose("/<int:folder_id>", methods=("DELETE",))
    @protect()
    @safe
    def delete(self, folder_id: int) -> Response:
        try:
            folder = FolderDAO.find_by_id(folder_id)
            if not folder:
                return self.response_404()

            user = get_user()

            # Validate folder deletion permissions
            can_modify_folder(folder, user)

            FolderDAO.delete([folder])
            db.session.commit()

            return self.response(200, message="Folder deleted")
        except Exception as e:
            return self.response_400(message=str(e))
