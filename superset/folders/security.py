# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
from flask_appbuilder.security.sqla.models import User

from superset.extensions import security_manager
from superset.folders.models import Folder, FolderLevel, FolderType


def has_folders_admin_permission() -> bool:
    """Check if user has folders admin permission"""
    if security_manager.is_folders_admin() or security_manager.is_admin():
        return True
    return False


def can_modify_folder(folder: Folder, user: User) -> bool:
    """Check if user can modify a specific folder"""
    # No one can modify ROOT or SYSTEM level folders
    if folder.level != FolderLevel.USER.value:
        return False

    # For PERSONAL folders, only the owner can modify
    if folder.type == FolderType.PERSONAL.value:
        return folder.created_by_fk == user.id

    # For GLOBAL/PLUGIN folders, only users with folders admin permission can modify
    if folder.type in (FolderType.GLOBAL.value, FolderType.PLUGIN.value):
        return has_folders_admin_permission()

    # Default deny
    return False


def can_update_folder_content(folder: Folder, user: User) -> bool:
    """Check if user can update content of a folder"""
    # No one can modify ROOT level folders
    if folder.level == FolderLevel.ROOT.value:
        return False

    # For PERSONAL folders, only the owner can modify content
    if folder.type == FolderType.PERSONAL.value:
        if folder.level == FolderLevel.SYSTEM.value:
            return True
        return folder.created_by_fk == user.id

    # For GLOBAL/PLUGIN folders, only users with folders admin permission can modify content
    if folder.type in (FolderType.GLOBAL.value, FolderType.PLUGIN.value):
        return has_folders_admin_permission()

    # Default deny
    return False
