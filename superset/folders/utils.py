import copy
from typing import Any

from superset.daos.dashboard import <PERSON><PERSON><PERSON><PERSON>
from superset.daos.folders import FolderDAO, FolderItemDAO
from superset.folders.models import Folder, FolderItemType, FolderType
from superset import db
from superset.folders.security import can_modify_folder
from flask_appbuilder.security.sqla.models import User


def get_child_folders(parent_id: int, folders: list[Folder]) -> list[int]:
    children_map = {}
    for folder in folders:
        if folder.parent_id not in children_map:
            children_map[folder.parent_id] = []
        children_map[folder.parent_id].append(folder.id)

    result = {parent_id}

    def collect_children(folder_id: int):
        if folder_id in children_map:
            for child_id in children_map[folder_id]:
                if child_id not in result:
                    result.add(child_id)
                    collect_children(child_id)

    collect_children(parent_id)
    return list(result)


def _build_folder_tree(folders: list, items: list[dict], root_id: int) -> Folder:
    folders = copy.deepcopy(folders)
    folders_hash = {folder.id: folder for folder in folders}

    for item in items + folders:
        parent_id = item.parent_id if isinstance(item, Folder) else item["folder_id"]
        if parent_id not in folders_hash:
            continue

        folder = folders_hash[parent_id]
        if not hasattr(folder, "children"):
            folder.children = []

        folder.children.append(item)

    for folder in folders:
        if hasattr(folder, "children"):
            folder.children.sort(key=lambda x: x.order if isinstance(x, Folder) else x["order"])

    return folders_hash[root_id]


def get_folder_tree(folder: Folder, user_id: int, team_id: int | None = None) -> list[Folder]:
    folders = FolderDAO.get_by_type(user_id=user_id, type=FolderType(folder.type), team_id=team_id)
    child_folders = get_child_folders(folder.id, folders)
    items = FolderItemDAO.get_by_folders(child_folders, user_id=user_id, team_id=team_id)

    return _build_folder_tree(folders, items, folder.id)


def validate_folder_content(parent_folder: Folder, content: list[dict], user: User) -> bool:
    # validate uniqueness by type and id
    unique = {(item["type"], item["id"]) for item in content}
    if len(unique) != len(content):
        return False

    # validate content
    for item in content:
        dao = FolderDAO() if item["type"] == FolderItemType.FOLDER.value else DashboardDAO()
        item_obj = dao.find_by_id(item["id"])
        if not item_obj:
            return False

        if item["type"] == FolderItemType.FOLDER.value:
            if item_obj.type != parent_folder.type:
                return False
            if not can_modify_folder(item_obj, user):
                return False

    return True


def add_order_to_content(content: list[dict]) -> list[dict]:
    for i in range(len(content)):
        content[i]["order"] = i
    return content


def calculate_folder_diff(current_content: list[dict], new_content: list[dict]) -> dict[str, Any]:
    """
    Calculate the difference between current and new folder content

    Args:
        current_content: List of current folder items in format [{"type": 0|1, "id": int, "order": int}, ...]
        new_content: List of new folder items in format [{"type": 0|1, "id": int, "order": int}, ...]

    Returns:
        Dictionary with items to add, remove, and update order
    """
    current_map = {(item["type"], item["id"]): item for item in current_content}
    new_map = {(item["type"], item["id"]): item for item in new_content}

    to_add = new_map.keys() - current_map.keys()

    to_remove = current_map.keys() - new_map.keys()

    to_update = []
    for key in set(current_map.keys()) & set(new_map.keys()):
        if current_map[key]["order"] != new_map[key]["order"]:
            to_update.append(new_map[key])

    return {
        "add": [new_map[key] for key in to_add],
        "remove": [current_map[key] for key in to_remove],
        "update": to_update
    }


def reorder_folder_items(folder_id: int, changes: dict) -> None:
    """
    Apply changes to folder items based on diff

    Args:
        folder_id: ID of the folder to update
        changes: Dictionary with changes from calculate_folder_diff
    """
    for item in changes["add"]:
        if item["type"] == FolderItemType.FOLDER.value:
            if folder := FolderDAO.find_by_id(item["id"]):
                folder.parent_id = folder_id
                folder.order = item["order"]
                db.session.add(folder)
        else:
            folder_item = FolderItemDAO.create(attributes={
                "folder_id": folder_id,
                "item_id": item["id"],
                "item_type": item["type"],
                "order": item["order"]
            })
            db.session.add(folder_item)

    for item in changes["remove"]:
        if item["type"] == FolderItemType.FOLDER.value:
            # Do not delete folder
            continue
        else:
            if folder_item := FolderItemDAO.find(folder_id, item["id"], item["type"]):
                FolderItemDAO.delete([folder_item])

    for item in changes["update"]:
        if item["type"] == FolderItemType.FOLDER.value:
            if folder := FolderDAO.find_by_id(item["id"]):
                folder.order = item["order"]
                db.session.add(folder)
        else:
            if folder_item := FolderItemDAO.find(folder_id, item["id"], item["type"]):
                folder_item.order = item["order"]
                db.session.add(folder_item)

    db.session.commit()
