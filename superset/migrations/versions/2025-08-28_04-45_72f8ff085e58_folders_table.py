# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
"""Folders table

Revision ID: 72f8ff085e58
Revises: 34fc36f98742
Create Date: 2025-08-28 04:45:20.992480

"""

import random

import sqlalchemy as sa
from alembic import op
from flask_appbuilder.security.sqla.models import User
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

from superset.folders.models import (
    Folder,
    FolderItem,
    FolderItemType,
    FolderLevel,
    FolderType,
)
from superset.models.dashboard import Dashboard

# revision identifiers, used by Alembic.
revision = "72f8ff085e58"
down_revision = "34fc36f98742"

Base = declarative_base()


class Team(Base):
    """Local Team model for migration"""

    __tablename__ = "teams"

    id = sa.Column(sa.Integer, primary_key=True)
    name = sa.Column(sa.String, nullable=False)
    is_external = sa.Column(sa.Boolean, nullable=False)
    slug = sa.Column(sa.String, unique=True)
    tag_id = sa.Column(sa.Integer, nullable=True)


def upgrade():
    op.create_table(
        "folders",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("name_ru", sa.String, nullable=False),
        sa.Column("name_en", sa.String, nullable=False),
        sa.Column("description_ru", sa.String, nullable=False),
        sa.Column("description_en", sa.String, nullable=False),
        sa.Column("type", sa.Integer, nullable=False),
        sa.Column("level", sa.Integer, nullable=False),
        sa.Column("slug", sa.String, nullable=True),
        sa.Column("order", sa.Integer, nullable=False, default=0),
        sa.Column("team_id", sa.Integer, nullable=True),
        sa.Column("parent_id", sa.Integer, nullable=True),
        sa.Column("created_on", sa.DateTime, nullable=True),
        sa.Column("changed_on", sa.DateTime, nullable=True),
        sa.Column("created_by_fk", sa.Integer, nullable=True),
        sa.Column("changed_by_fk", sa.Integer, nullable=True),
        sa.ForeignKeyConstraint(["created_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["changed_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["team_id"], ["teams.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["parent_id"], ["folders.id"], ondelete="CASCADE"),
    )
    op.create_table(
        "folder_items",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("folder_id", sa.Integer, nullable=False),
        sa.Column("item_id", sa.Integer, nullable=False),
        sa.Column("item_type", sa.Integer, nullable=False),
        sa.Column("order", sa.Integer, nullable=False, default=0),
        sa.Column("created_on", sa.DateTime, nullable=True),
        sa.Column("changed_on", sa.DateTime, nullable=True),
        sa.Column("created_by_fk", sa.Integer, nullable=True),
        sa.Column("changed_by_fk", sa.Integer, nullable=True),
        sa.ForeignKeyConstraint(["created_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["changed_by_fk"], ["ab_user.id"]),
        sa.ForeignKeyConstraint(["folder_id"], ["folders.id"], ondelete="CASCADE"),
    )
    op.create_index("idx_folder_items_folder_id", "folder_items", ["folder_id"])
    op.create_index("idx_folder_items_item_id", "folder_items", ["item_id"])
    op.create_index("idx_folder_items_item_type", "folder_items", ["item_type"])

    # create root folder
    global_folder = Folder(
        name_ru="global",
        name_en="global",
        description_ru="Global folder",
        description_en="Global folder",
        type=FolderType.GLOBAL.value,
        level=FolderLevel.ROOT.value,
        slug="global",
        order=0,
    )

    bind = op.get_bind()
    session = Session(bind=bind)

    session.add(global_folder)
    session.flush()

    # create system folders
    personal_folder = Folder(
        name_ru="personal",
        name_en="personal",
        description_ru="Personal folder",
        description_en="Personal folder",
        type=FolderType.PERSONAL.value,
        level=FolderLevel.SYSTEM.value,
        slug="personal",
        order=0,
    )
    team_folder = Folder(
        name_ru="team",
        name_en="team",
        description_ru="Team folder",
        description_en="Team folder",
        type=FolderType.TEAM.value,
        level=FolderLevel.SYSTEM.value,
        slug="team",
        order=1,
    )

    # Global SYSTEM folders
    global_pizza_folder = Folder(
        name_ru="pizza",
        name_en="pizza",
        description_ru="Pizza folder",
        description_en="Pizza folder",
        type=FolderType.GLOBAL.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=global_folder.id,
        slug="global_pizza",
        order=0,
    )
    global_drinkit_folder = Folder(
        name_ru="drinkit",
        name_en="drinkit",
        description_ru="Drinkit folder",
        description_en="Drinkit folder",
        type=FolderType.GLOBAL.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=global_folder.id,
        slug="global_drinkit",
        order=1,
    )

    # Plugin ROOT folder
    plugin_folder = Folder(
        name_ru="plugin",
        name_en="plugin",
        description_ru="Plugin folder",
        description_en="Plugin folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.ROOT.value,
        slug="plugin",
        order=2,
    )
    session.add(plugin_folder)
    session.flush()

    # Analytics ROOT folder (inside Plugin)
    analytics_folder = Folder(
        name_ru="analytics",
        name_en="analytics",
        description_ru="Analytics folder",
        description_en="Analytics folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.ROOT.value,
        parent_id=plugin_folder.id,
        slug="plugin_analytics",
        order=0,
    )

    session.add(personal_folder)
    session.add(team_folder)
    session.add(global_pizza_folder)
    session.add(global_drinkit_folder)
    session.add(analytics_folder)
    session.commit()

    # try:
    #     for team in session.query(Team).all():
    #         local_team_directory = Directory(
    #             name_ru=team.slug,
    #             name_en=team.slug,
    #             description_ru=f"Team directory for {team.name}",
    #             description_en=f"Team directory for {team.name}",
    #             type=DirectoryType.TEAM.value,
    #             level=DirectoryLevel.SYSTEM.value,
    #             parent_id=team_directory.id,
    #         )
    #         session.add(local_team_directory)
    #         session.flush()

    #     session.commit()

    # except Exception as e:
    #     print(f"Error during migration: {str(e)}")
    #     session.rollback()
    #     raise
    # finally:
    #     session.close()

    # Create USER level folders with pizza and drinkit subdirs
    dashboards = session.query(Dashboard).all()

    # Create pizza and drinkit SYSTEM folders inside analytics
    analytics_pizza_folder = Folder(
        name_ru="pizza",
        name_en="pizza",
        description_ru="Pizza folder",
        description_en="Pizza folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=analytics_folder.id,
        slug="plugin_analytics_pizza",
        order=0,
    )
    analytics_drinkit_folder = Folder(
        name_ru="drinkit",
        name_en="drinkit",
        description_ru="Drinkit folder",
        description_en="Drinkit folder",
        type=FolderType.PLUGIN.value,
        level=FolderLevel.SYSTEM.value,
        parent_id=analytics_folder.id,
        slug="plugin_analytics_drinkit",
        order=1,
    )

    session.add(analytics_pizza_folder)
    session.add(analytics_drinkit_folder)
    session.flush()

    # Create USER level folders inside pizza and drinkit
    for _ in range(3):
        # USER folders inside pizza
        pizza_user_folder = Folder(
            name_ru=f"pizza_user_{_}",
            name_en=f"pizza_user_{_}",
            description_ru=f"Pizza user folder {_}",
            description_en=f"Pizza user folder {_}",
            type=FolderType.PLUGIN.value,
            level=FolderLevel.USER.value,
            parent_id=analytics_pizza_folder.id,
            order=_,
        )
        session.add(pizza_user_folder)
        session.flush()

        # Add some dashboards to pizza user folders
        for __ in range(random.randint(1, 3)):
            pizza_item = FolderItem(
                folder_id=pizza_user_folder.id,
                item_id=random.choice(dashboards).id,
                item_type=FolderItemType.DASHBOARD.value,
                order=__,
            )
            session.add(pizza_item)

        # USER folders inside drinkit
        drinkit_user_folder = Folder(
            name_ru=f"drinkit_user_{_}",
            name_en=f"drinkit_user_{_}",
            description_ru=f"Drinkit user folder {_}",
            description_en=f"Drinkit user folder {_}",
            type=FolderType.PLUGIN.value,
            level=FolderLevel.USER.value,
            parent_id=analytics_drinkit_folder.id,
            order=_,
        )
        session.add(drinkit_user_folder)
        session.flush()

        # Add some dashboards to drinkit user folders
        for __ in range(random.randint(1, 3)):
            drinkit_item = FolderItem(
                folder_id=drinkit_user_folder.id,
                item_id=random.choice(dashboards).id,
                item_type=FolderItemType.DASHBOARD.value,
                order=__,
            )
            session.add(drinkit_item)

    # Create USER level folders inside global pizza and drinkit
    for _ in range(2):
        # USER folders inside global pizza
        global_pizza_user_folder = Folder(
            name_ru=f"global_pizza_user_{_}",
            name_en=f"global_pizza_user_{_}",
            description_ru=f"Global pizza user folder {_}",
            description_en=f"Global pizza user folder {_}",
            type=FolderType.GLOBAL.value,
            level=FolderLevel.USER.value,
            parent_id=global_pizza_folder.id,
            order=_,
        )
        session.add(global_pizza_user_folder)
        session.flush()

        # Add some dashboards to global pizza user folders
        for __ in range(random.randint(1, 3)):
            global_pizza_item = FolderItem(
                folder_id=global_pizza_user_folder.id,
                item_id=random.choice(dashboards).id,
                item_type=FolderItemType.DASHBOARD.value,
                order=__,
            )
            session.add(global_pizza_item)

        # USER folders inside global drinkit
        global_drinkit_user_folder = Folder(
            name_ru=f"global_drinkit_user_{_}",
            name_en=f"global_drinkit_user_{_}",
            description_ru=f"Global drinkit user folder {_}",
            description_en=f"Global drinkit user folder {_}",
            type=FolderType.GLOBAL.value,
            level=FolderLevel.USER.value,
            parent_id=global_drinkit_folder.id,
            order=_,
        )
        session.add(global_drinkit_user_folder)
        session.flush()

        # Add some dashboards to global drinkit user folders
        for __ in range(random.randint(1, 3)):
            global_drinkit_item = FolderItem(
                folder_id=global_drinkit_user_folder.id,
                item_id=random.choice(dashboards).id,
                item_type=FolderItemType.DASHBOARD.value,
                order=__,
            )
            session.add(global_drinkit_item)

    # Create personal USER folders
    for _ in range(5):
        users = [user.id for user in session.query(User).all() if user.id != 1]
        personal_user_folder = Folder(
            name_ru=f"personal_user_{_}",
            name_en=f"personal_user_{_}",
            description_ru=f"Personal user folder {_}",
            description_en=f"Personal user folder {_}",
            type=FolderType.PERSONAL.value,
            level=FolderLevel.USER.value,
            parent_id=personal_folder.id,
            created_by_fk=random.choice([1, random.choice(users) if users else 1]),
            order=_,
        )
        session.add(personal_user_folder)
        session.flush()

        # Add some dashboards to personal user folders
        for __ in range(random.randint(1, 4)):
            personal_item = FolderItem(
                folder_id=personal_user_folder.id,
                item_id=random.choice(dashboards).id,
                item_type=FolderItemType.DASHBOARD.value,
                created_by_fk=personal_user_folder.created_by_fk,
                order=__,
            )
            session.add(personal_item)

    session.commit()


def downgrade():
    op.drop_index("idx_folder_items_item_type", table_name="folder_items")
    op.drop_index("idx_folder_items_item_id", table_name="folder_items")
    op.drop_index("idx_folder_items_folder_id", table_name="folder_items")
    op.drop_table("folder_items")
    op.drop_table("folders")
