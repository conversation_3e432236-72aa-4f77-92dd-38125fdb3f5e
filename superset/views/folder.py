from flask import redirect, url_for
from flask_appbuilder import expose
from flask_appbuilder.security.decorators import has_access

from superset.superset_typing import FlaskResponse
from superset.views.base import BaseSupersetView
# from superset.views.base import permission_name


class FolderModelView(BaseSupersetView):
    route_base = "/folder"
    class_permission_name = "Folder"

    @expose("/")
    def index(self):
        return redirect(url_for("FolderModelView.get", pk="personal")) 

    @expose("/<string:pk>/")
    # @has_access
    # @permission_name("read")
    def get(self, pk: str) -> FlaskResponse:  # pylint: disable=unused-argument
        return super().render_app_template()
