from __future__ import annotations

import unittest
from unittest import mock

from superset.daos.org_unit import OrgUnitDAO
from superset.orgstructure.models import OrgUnit


class TestOrgUnitDAO(unittest.TestCase):
    """Tests for the OrgUnit DAO."""

    @mock.patch("superset.daos.org_unit.db.session")
    def test_upsert_from_payload_new_unit(self, mock_session):
        """Test upserting a new unit."""
        # Mock query to return None (no existing unit)
        mock_query = mock.MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value.one_or_none.return_value = None

        # Test payload
        payload = {
            "id": "TEST123",
            "country_id": 123,
            "department_uuid": "dep-uuid-123",
            "name": "Test Unit",
            "alias": "Test",
            "type": 1,
            "is_removed": False,
            "version": 5,
            "monolith_id": "m123",
        }

        # Call the method
        result = OrgUnitDAO.upsert_from_payload(payload)

        # Verify the session.add was called (new unit)
        mock_session.add.assert_called_once()
        # Verify commit was called
        mock_session.commit.assert_called_once()
        # Verify the result is the new unit
        self.assertIsNotNone(result)

    @mock.patch("superset.daos.org_unit.db.session")
    def test_upsert_from_payload_existing_unit_newer_version(self, mock_session):
        """Test upserting an existing unit with a newer version."""
        # Mock existing unit
        existing_unit = OrgUnit(
            id="TEST123",
            country_id=123,
            department_uuid="dep-uuid-123",
            name="Old Name",
            alias="Old",
            type=1,
            is_removed=False,
            version=3,  # Old version
            monolith_id="m123",
        )

        # Mock query to return existing unit
        mock_query = mock.MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value.one_or_none.return_value = existing_unit

        # Test payload with newer version
        payload = {
            "id": "TEST123",
            "country_id": 123,
            "department_uuid": "dep-uuid-123",
            "name": "New Name",  # Updated name
            "alias": "New",  # Updated alias
            "type": 1,
            "is_removed": False,
            "version": 5,  # Newer version
            "monolith_id": "m123",
        }

        # Call the method
        result = OrgUnitDAO.upsert_from_payload(payload)

        # Verify add was not called (existing unit)
        mock_session.add.assert_not_called()
        # Verify commit was called
        mock_session.commit.assert_called_once()
        # Verify the unit was updated
        self.assertEqual(result.name, "New Name")
        self.assertEqual(result.alias, "New")
        self.assertEqual(result.version, 5)

    @mock.patch("superset.daos.org_unit.db.session")
    def test_upsert_from_payload_existing_unit_older_version(self, mock_session):
        """Test upserting an existing unit with an older version."""
        # Mock existing unit
        existing_unit = OrgUnit(
            id="TEST123",
            country_id=123,
            department_uuid="dep-uuid-123",
            name="Current Name",
            alias="Current",
            type=1,
            is_removed=False,
            version=5,  # Newer version
            monolith_id="m123",
        )

        # Mock query to return existing unit
        mock_query = mock.MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value.one_or_none.return_value = existing_unit

        # Test payload with older version
        payload = {
            "id": "TEST123",
            "country_id": 123,
            "department_uuid": "dep-uuid-123",
            "name": "Old Name",  # Should not update
            "alias": "Old",  # Should not update
            "type": 1,
            "is_removed": False,
            "version": 3,  # Older version
            "monolith_id": "m123",
        }

        # Call the method
        result = OrgUnitDAO.upsert_from_payload(payload)

        # Verify the unit was not updated
        self.assertEqual(result.name, "Current Name")
        self.assertEqual(result.alias, "Current")
        self.assertEqual(result.version, 5)
        # Verify add was not called (existing unit)
        mock_session.add.assert_not_called()
        # Verify commit was not called (no changes)
        mock_session.commit.assert_not_called()

    @mock.patch("superset.daos.org_unit.db.session")
    def test_upsert_from_payload_exception(self, mock_session):
        """Test exception handling during upsert."""
        # Mock query to raise exception
        mock_session.query.side_effect = Exception("Test error")

        # Test payload
        payload = {
            "id": "TEST123",
            "country_id": 123,
            "department_uuid": "dep-uuid-123",
            "name": "Test Unit",
            "alias": "Test",
            "type": 1,
            "is_removed": False,
            "version": 5,
            "monolith_id": "m123",
        }

        # Call the method
        result = OrgUnitDAO.upsert_from_payload(payload)

        # Verify result is None
        self.assertIsNone(result)
        # Verify rollback was called
        mock_session.rollback.assert_called_once()

    @mock.patch("superset.daos.org_unit.db.session")
    def test_get_departments_from_claims_empty(self, mock_session):
        """Test getting departments with empty claims."""
        result = OrgUnitDAO.get_departments_from_claims([], None)
        self.assertEqual(result, [])

    @mock.patch("superset.daos.org_unit.db.session")
    def test_get_departments_from_claims_department(self, mock_session):
        """Test getting departments from department claims."""
        # Call the method with a department claim
        result = OrgUnitDAO.get_departments_from_claims(["DEP123:1:3"], None)

        # Verify result contains the department
        self.assertEqual(result, ["DEP123"])
        # Verify query was not called (no need to query for department claims)
        mock_session.query.assert_not_called()

    @mock.patch("superset.daos.org_unit.OrgUnitDAO.get_departments_by_country_ids")
    def test_get_departments_from_claims_country(self, mock_get_departments):
        """Test getting departments from country claims."""
        # Mock get_departments_by_country_ids to return departments
        mock_get_departments.return_value = ["DEP1", "DEP2"]

        # Call the method with a country claim (643 is hex for 1603)
        result = OrgUnitDAO.get_departments_from_claims(["643:3:1"], "MID123")

        # Verify result contains the departments
        self.assertIn("DEP1", result)
        self.assertIn("DEP2", result)
        # Verify get_departments_by_country_ids was called with correct parameters
        mock_get_departments.assert_called_once_with(
            [1603], "MID123"
        )  # 643 hex = 1603 decimal

    @mock.patch("superset.daos.org_unit.db.session")
    def test_get_departments_from_claims_unit_ignored(self, mock_session):
        """Test that unit claims are ignored."""
        # Call the method with a unit claim
        result = OrgUnitDAO.get_departments_from_claims(["UNIT123:2:4"], None)

        # Verify result is empty (unit claims are ignored)
        self.assertEqual(result, [])
        # Verify query was not called
        mock_session.query.assert_not_called()

    @mock.patch("superset.daos.org_unit.OrgUnitDAO.get_departments_by_country_ids")
    def test_get_departments_from_claims_multiple_types(self, mock_get_departments):
        """Test getting departments from multiple claim types."""
        # Mock get_departments_by_country_ids to return departments
        mock_get_departments.return_value = ["DEP3", "DEP4"]

        # Call the method with multiple claim types
        result = OrgUnitDAO.get_departments_from_claims(
            ["DEP123:1:3", "643:3:1", "UNIT123:2:4"], "MID123"
        )

        # Verify result contains all departments
        self.assertIn("DEP123", result)  # From department claim
        self.assertIn("DEP3", result)  # From country query
        self.assertIn("DEP4", result)  # From country query
        # Verify UNIT123 is not in result (unit claims are ignored)
        self.assertNotIn("UNIT123", result)

        # Verify get_departments_by_country_ids was called once (for country claim)
        mock_get_departments.assert_called_once_with(
            [1603], "MID123"
        )  # 643 hex = 1603 decimal
